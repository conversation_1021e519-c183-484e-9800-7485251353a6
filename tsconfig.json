{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ES2022", "DOM", "DOM.Iterable"], "esModuleInterop": true, "strict": false, "noImplicitAny": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "allowJs": true, "checkJs": false, "outDir": "dist", "rootDir": "src", "jsx": "react", "declaration": false, "sourceMap": true, "baseUrl": ".", "paths": {"*": ["node_modules/*", "src/types/*", "src/*"]}, "types": ["bun-types", "node"], "allowSyntheticDefaultImports": true, "noEmitOnError": false, "ignoreDeprecations": "5.0", "useUnknownInCatchVariables": false, "noErrorTruncation": true, "noEmit": false, "skipDefaultLibCheck": true, "isolatedModules": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noFallthroughCasesInSwitch": false, "noImplicitReturns": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noUnusedLocals": false, "noUnusedParameters": false, "plugins": [{"transform": "ts-transform-define", "type": "config", "config": {"MACRO": "src/constants/macros.ts"}}]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node", "transpileOnly": true, "compilerOptions": {"module": "NodeNext"}}}