{"name": "@shareai-lab/kode", "version": "1.0.71", "bin": {"kode": "cli.js", "kwa": "cli.js", "kd": "cli.js"}, "engines": {"node": ">=18.0.0"}, "main": "cli.js", "author": "ShareAI-lab <<EMAIL>>", "license": "ISC", "description": "AI-powered terminal assistant that understands your codebase, edits files, runs commands, and automates development workflows.", "homepage": "https://github.com/shareAI-lab/kode", "repository": {"type": "git", "url": "git+https://github.com/shareAI-lab/kode.git"}, "bugs": {"url": "https://github.com/shareAI-lab/kode/issues"}, "files": ["cli.js", "yoga.wasm", "src/**/*", "scripts/postinstall.js", ".npmrc"], "scripts": {"dev": "bun run ./src/entrypoints/cli.tsx --verbose", "build": "bun run scripts/build.ts", "clean": "rm -rf cli.js", "prepublishOnly": "bun run build && node scripts/prepublish-check.js", "postinstall": "node scripts/postinstall.js || true", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json}\"", "test": "bun test", "typecheck": "tsc --noEmit"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.12.6", "@anthropic-ai/sdk": "^0.39.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@commander-js/extra-typings": "^13.1.0", "@inkjs/ui": "^2.0.0", "@modelcontextprotocol/sdk": "^1.15.1", "@statsig/js-client": "^3.18.2", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.8", "ansi-escapes": "^7.0.0", "chalk": "^5.4.1", "cli-highlight": "^2.1.11", "cli-table3": "^0.6.5", "commander": "^13.1.0", "debug": "^4.4.1", "diff": "^7.0.0", "dotenv": "^16.6.1", "env-paths": "^3.0.0", "figures": "^6.1.0", "glob": "^11.0.3", "highlight.js": "^11.11.1", "ink": "^5.2.1", "ink-link": "^4.1.0", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "marked": "^15.0.12", "nanoid": "^5.1.5", "openai": "^4.104.0", "react": "18.3.1", "semver": "^7.7.2", "shell-quote": "^1.8.3", "spawn-rx": "^5.1.2", "tsx": "^4.20.3", "undici": "^7.11.0", "wrap-ansi": "^9.0.0", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.1.0", "bun-types": "latest", "prettier": "^3.6.2", "typescript": "^5.9.2"}, "overrides": {"string-width": "^7.2.0", "strip-ansi": "^7.1.0"}, "directories": {"doc": "docs", "test": "test"}, "keywords": ["cli", "ai", "assistant", "agent", "kode", "shareai", "terminal", "command-line"], "bundledDependencies": ["tsx"]}