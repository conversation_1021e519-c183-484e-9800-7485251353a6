export const largeAnimatedAray = [
  `                                                  
              .=#*=.      :==.                    
              -%%%%=.    .#%#=                    
              .=%%%#=    :%%#:    -=+-            
         ...   .=%%%*-   =@%+   :+%%%%.           
        :*%%+=  .=%%%*-  +%%= .=%%%%%=            
        .=#%%%#=..=#%%*: *%#:-*%%%%+:             
          .=*%%%%+==#%%+.%%+=#%%%%=.              
             :=#%%%##%%%*%%%%%%%*-       .        
                -=#%%%%%%%%%%%%+-====+*%%%+.      
     .============-=*%%%%%%%%%%%%%%%%#+===:       
      =======+++****%%%%%%%%%%#+==:.              
                  -=*%%%%%%%%%*+#%%%%%%%#*=.      
              .=+#%#++#%%%%%%%%+-..-==+*##=.      
           .=+%%%+=-+%#=*%+%%%##%+:               
         .+%%%*=. =*%+:-%%:=#%#==#%+:             
         .=+=.  .=%%=. +%#. -*%%=:=*%+-           
               -*%#=  .#%*   :*%%+: :=*.          
             .=%%=.   =%%=    .=%%=.              
              :=.     +%%=     .-=:               
                      =#+.                        
`,
  `                                                  
              .=*+=.      .==.                    
              -####=.    .*#*=                    
              .=###*-    :##*:    -==-            
         ...   .=###+-   =%#+   :+####.           
        .+##+-  .=###+:  =##= .=*####-            
        .=*###*=..=*##+. +#*::+####=.             
          .=+###*=-=*##+.*#==*###*=.              
             .=*###**###+#######+-                
                :=*############+--====*###=.      
     .===========--=+################*+===.       
      -=========++++##########*+==.               
                  :=*#########*+*#######*+=.      
              .==*#*==*########=-..-===+**=.      
           .==*##+=:=#*-*#+###**#+:               
         .=###+=. -+#+::##:=*#*==*#=:             
         .===.  .=##=. =#*. -+#*=:=+#=-           
               -+#*=  .*#+   :+##+: :=*.          
              =#*=.   =##=    .=##=.              
              :=.     =##=      -=:               
                      =*+.                        
`,
  `                                                  
              .=+==.      .=-.                    
              :****=     .+*+=                    
              .=***+-    :**+:    -==:            
         ...   .=***+:   -**=   :=****.           
        .+**=-  .=***=:  =**= .=*****-            
        .=+***+=..=+**=. =*+.:=****=.             
           ==****=-=+**=.**==+****=.              
             .=+***++***+*******=:                
                :=+************=:-====+***=.      
     .==========--:-+****************+====.       
      -============+**********+==-.               
                  :=+*********+=+*******+==.      
              .-=+*+==+********=:..:====++=.      
            ==***=-:=*+-+*=***++*=:               
         .=***+=. -+*=::**.-+*+==+*=:             
         .===.  .=**=. =*+. :+**=.=+*=-           
               :+*+-  .+*+   :=**=: :=+.          
              =**=.   -**=    .=**=.              
              :-.     =**-      :=.               
                      =+=.                        
`,
  `                                                  
              .===-.      .=-.                    
              :++++=      =+=-                    
              .=+++=:    .++=:    :==:            
         ..    .=+++=:   -++=   :=++++            
        .=++=:  .=+++=:  =++= .=+++++:            
        .==+++==..==++=. =+=.:=++++=.             
           -=++++=--=++=.++=-=++++=.              
             .==+++==+++=+++++++=:                
                :==++++++++++++=::=====+++=.      
     .-====---=---:-=++++++++++++++++====-.       
      :=============++++++++++===-.               
                  :==+++++++++===+++++++==-.      
              .-==+===+++++++++=: .:=======.      
            -=+++=-:=+=:=+=+++==+=:               
         .=+++==. :=+=::++.-=+====+=.             
          ===.  .=++=. =++. :=++=.-=+=:           
               :=+=-  .++=   :=++=. .==.          
              -++=.   -++=    .=++=.              
              .-.     =++-      :-.               
                      -==.                        
`,
  `                                                  
              .===-.      .-:                     
              :====-      ===-                    
              .-====:    .===.    :==:            
         ..    .-====:   :===   .=====            
        .====:  .-====.  ===- .-=====:            
         -=====-..-====. ===..======.             
           -======:-====.===:=====-.              
             .-==================:                
                .===============::-========.      
     .-=---------:::=====================-.       
      :=-========================:.               
                  .=======================-.      
               :================: .:-=====-.      
            -=====:.===:==========.               
         .=====-. :===.:==.:===--===.             
          -=-.  .===-. ===  :====.-===:           
               :===:  .===   .====. .==           
              -==-.   :===    .====.              
              .:.     ===:      ::.               
                      -==.                        
`,
  `                                                  
              .-==:       .-:                     
              .====:      ===:                    
               :====:    .===.    .--.            
          .    .-====.   :===   .-====            
        .====.   :====.  -==: .:=====:            
         -=====-. :====. ===..=====-.             
           :======::====.==-:=====:               
             .-==================.                
                .-=============-..:---====-.      
     .:-------::::.:===================--:.       
      .---------===============--:.               
                  .-======================:       
               :-===--==========. ..:--===-.      
            :=====:.-==:==-=======.               
         .-====:. .===..==.:===::==-.             
          --:.  .-==-. -==  .===-.:==-.           
               .===:   ===   .====. .-=           
              :==-.   :==-    .-==-.              
              .:.     -==:      .:.               
                      :==                         
`,
  `                                                  
               :--:       .:.                     
              .===-:      -=-.                    
               :===-.    .==-.    .::.            
          .     :-==-.   .==:   .:-===            
        .-==:.   :-==-.  :=-:  :-===-.            
         :-===-:. :-==-. -=-..-====:.             
           .-===-:.:-==:.-=:.-===-:               
             .:-===---==:-==-=-=-.                
                .:-===========-:..::::--==-.      
      .:::::::::....--========-======-:::..       
      .:::::::::-----========--::..               
                  .:--====-------=======--.       
               .:-=-::---==--=-:.  .:::---:.      
            .:-=-:..:--.-=:-=---=-.               
          :===-:. .-=-..==..-=-::-=:.             
          :::.  .:=-:  :=-  .-=-:.:---.           
               .-=-.   -=-   .-==-. .:-           
              :=-:.   .-=:    .:-=:.              
              ...     :==.      ...               
                      .--                         
`,
  `                                                  
               .::.        ..                     
              .::::.      :::.                    
               .::::.     :::.    ....            
                .::::.   .::.   ..::::            
         :::..   .:::..  .::.  .:::::.            
         .:::::.  .:::.  .:: ..::::.              
           ..::::...:::. ::..:::::.               
              .:::::::::.:::::::..                
                ..:::::::::::::.......::::.       
      ..............::::::::::::::::::....        
      ...........::::::::::::::...                
                  ..:::::::::::.::::::::::.       
               ..:::..:::::::::..  .....::.       
            ..:::....::.::.::::::..               
          .::::.  .::...:: .:::..::.              
          ...    .::.  .::  .:::. .::..           
               .:::.   ::.   ..::.   .:           
              .::.    .::.     .::.               
               .      .::.      ..                
                      .:.                         
`,
  `                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
                                                  
`,
]

export const smallAnimatedArray = [
  `   @   
@  @  @
  @@@  
@  @  @
   @`,
  `   *   
*  *  *
  ***  
*  *  *
   *`,
  `   +   
+  +  +
  +++  
+  +  +
   +`,
  `   /   
/  /  /
  ///  
/  /  /
   /`,
  `   |   
|  |  |
  |||  
|  |  |
   |`,
  `   \\   
\\  \\  \\
  \\\\\\  
\\  \\  \\
   \\`,
  `   -   
-  -  -
  ---  
-  -  -
   -`,
]
